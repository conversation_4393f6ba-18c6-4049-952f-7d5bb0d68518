import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef } from 'react'
import { useTranslations } from 'next-intl'
import { yupResolver } from '@hookform/resolvers/yup'
import { useToastContext } from '@ninebot/core'
import { checkoutInsuranceItemId } from '@ninebot/core/src/store'
import { useAppSelector } from '@ninebot/core/src/store/hooks'
import { insuranceFormSchema } from '@ninebot/core/src/validations'
import { Card, Input } from 'antd'
import { Controller, FieldError, useForm } from 'react-hook-form'

interface InsuranceFormCardProps {
  scrollViewRef: React.RefObject<HTMLDivElement>
}

interface InsuranceFormData {
  username: string
  idCard: string
  phone: string
}

interface InsuranceFormRef {
  validate: () => Promise<{
    item_id: string
    id_card: string
    name: string
    telephone: string
  } | null>
  scrollTo: () => Promise<void>
}

/**
 * 保单表单组件
 */
const InsuranceFormCard = forwardRef<InsuranceFormRef, InsuranceFormCardProps>((props, ref) => {
  const getI18nString = useTranslations('Common')
  const { scrollViewRef } = props

  const toast = useToastContext()

  const insuranceItemId = useAppSelector(checkoutInsuranceItemId)

  const rootRef = useRef<HTMLDivElement>(null)

  /**
   * 初始化表单
   */
  const {
    control,
    trigger,
    clearErrors,
    getValues,
    formState: { errors },
  } = useForm<InsuranceFormData>({
    resolver: yupResolver(insuranceFormSchema),
    defaultValues: {
      username: '',
      idCard: '',
      phone: '',
    },
  })

  /**
   * 自定义输入框失焦验证
   */
  const onBlurHandler = useCallback(
    async (name: keyof InsuranceFormData) => {
      clearErrors()
      await trigger(name) // 手动触发验证
    },
    [clearErrors, trigger],
  )

  /**
   * 生成提交数据
   */
  const generateSubmitData = useCallback(
    (formData: InsuranceFormData) => {
      return {
        item_id: insuranceItemId || '',
        id_card: formData.idCard,
        name: formData.username,
        telephone: formData.phone,
      }
    },
    [insuranceItemId],
  )

  /**
   * 表单 errors 提示
   */
  useEffect(() => {
    // 只有当存在保险商品ID时才显示错误提示，避免在状态重置时显示不必要的错误
    if (insuranceItemId && errors && Object.keys(errors).length) {
      const filedKeys = Object.keys(errors) as Array<keyof InsuranceFormData>
      const firstErrorKey = filedKeys[0]
      const firstError = errors[firstErrorKey] as FieldError
      toast.show({
        content: firstError.message || '表单验证错误',
      })
    }
  }, [errors, toast, insuranceItemId])

  /**
   * 暴露 Api
   */
  useImperativeHandle(ref, () => {
    return {
      /**
       * 验证表单
       */
      validate: async () => {
        const isValid = await trigger()
        if (isValid) {
          return generateSubmitData(getValues())
        } else {
          return null
        }
      },
      /**
       * 滚动到表单位置
       */
      scrollTo: async () => {
        if (rootRef.current && scrollViewRef.current) {
          const rect = rootRef.current.getBoundingClientRect()
          const scrollTop = document.documentElement.scrollTop
          scrollViewRef.current.scrollTo({
            top: rect.top + scrollTop - 8,
            behavior: 'smooth',
          })
        }
      },
    }
  })

  return (
    <div ref={rootRef} className="w-full">
      <Card title={getI18nString('insurance_user_info')} className="w-full">
        <div className="space-y-4">
          <Controller
            control={control}
            name="username"
            render={({ field: { onChange, value } }) => (
              <div className="flex flex-col space-y-2">
                <label className="text-gray-700">
                  {getI18nString('name')}
                  <span className="ml-1 text-red-500">*</span>
                </label>
                <Input
                  className="w-full"
                  placeholder={getI18nString('please_enter_name')}
                  maxLength={10}
                  value={value}
                  onChange={onChange}
                  onBlur={() => onBlurHandler('username')}
                />
              </div>
            )}
          />
          <Controller
            control={control}
            name="idCard"
            render={({ field: { onChange, value } }) => (
              <div className="flex flex-col space-y-2">
                <label className="text-gray-700">
                  {getI18nString('id_card')}
                  <span className="ml-1 text-red-500">*</span>
                </label>
                <Input
                  className="w-full"
                  placeholder={getI18nString('please_enter_id_card')}
                  maxLength={18}
                  value={value}
                  onChange={onChange}
                  onBlur={() => onBlurHandler('idCard')}
                />
              </div>
            )}
          />
          <Controller
            control={control}
            name="phone"
            render={({ field: { onChange, value } }) => (
              <div className="flex flex-col space-y-2">
                <label className="text-gray-700">
                  {getI18nString('phone_number')}
                  <span className="ml-1 text-red-500">*</span>
                </label>
                <div className="flex items-center">
                  <span className="mr-2 text-gray-600">+86</span>
                  <Input
                    addonBefore="+86"
                    className="w-full"
                    placeholder={getI18nString('please_enter_phone')}
                    maxLength={11}
                    value={value}
                    onChange={onChange}
                    onBlur={() => onBlurHandler('phone')}
                  />
                </div>
              </div>
            )}
          />
          <div className="mt-4 text-sm text-gray-500">* {getI18nString('insurance_tip')}</div>
        </div>
      </Card>
    </div>
  )
})

InsuranceFormCard.displayName = 'InsuranceFormCard'

export default InsuranceFormCard
